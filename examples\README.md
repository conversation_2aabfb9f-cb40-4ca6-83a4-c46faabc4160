# CIC<PERSON>lowMeter ICMP Support Examples

This directory contains example PCAP files and their corresponding flow analysis outputs demonstrating the enhanced ICMP support in CICFlowMeter.

## Files Overview

### Input PCAP Files
- **`mixed_traffic.pcap`** - Contains mixed TCP, UDP, and ICMP traffic (12 packets)
- **`icmp_only.pcap`** - Contains only ICMP traffic of various types (15 packets)
- **`icmp_flood.pcap`** - Original test file (if present)

### Generated Output Files
- **`mixed_traffic_flows.csv`** - Flow analysis results for mixed traffic
- **`mixed_traffic_summary.txt`** - Statistical summary of mixed traffic analysis
- **`icmp_only_flows.csv`** - Flow analysis results for ICMP-only traffic
- **`icmp_only_summary.txt`** - Statistical summary of ICMP-only traffic analysis

## Analysis Results

### Mixed Traffic Analysis (`mixed_traffic.pcap`)
- **Total Flows**: 5
- **Features per Flow**: 82 (original count maintained)
- **Flow Distribution**:
  - ICMP Flows: 3
  - TCP Flows: 1
  - UDP Flows: 1

**ICMP Flow Details**:
- Unique Source IPs: 3
- Unique Destination IPs: 2
- Includes ping traffic (echo request/reply pairs)
- Includes ICMP unreachable and TTL exceeded messages

### ICMP-Only Analysis (`icmp_only.pcap`)
- **Total Flows**: 4
- **Features per Flow**: 82 (original count maintained)
- **Flow Distribution**:
  - ICMP Flows: 4 (100%)
  - TCP Flows: 0
  - UDP Flows: 0

**ICMP Flow Details**:
- Unique Source IPs: 2
- Unique Destination IPs: 2
- Includes ping flood simulation
- Includes various ICMP error types

## Key Validation Points

✅ **ICMP packets successfully processed**
- All ICMP packet types are correctly identified and processed
- Echo request/reply pairs are properly grouped as bidirectional flows

✅ **Original 82 features maintained**
- No additional fields added to maintain backward compatibility
- Existing ML pipelines can use the output without modification

✅ **Non-applicable fields set to -1 for ICMP flows**
- `src_port` and `dst_port` set to -1 for ICMP flows
- TCP-specific flags (`fin_flag_cnt`, `syn_flag_cnt`, etc.) set to -1
- TCP window sizes (`init_fwd_win_byts`, `init_bwd_win_byts`) set to -1

✅ **Backward compatibility with TCP/UDP preserved**
- TCP and UDP flows processed exactly as before
- All existing features work normally for TCP/UDP traffic

## Sample Flow Data

### ICMP Flow (Echo Request/Reply)
```csv
src_ip,dst_ip,src_port,dst_port,protocol,tot_fwd_pkts,tot_bwd_pkts,flow_duration,fin_flag_cnt
*************,*******,-1,-1,1,4,3,2.05,-1
```

### TCP Flow (Unchanged)
```csv
src_ip,dst_ip,src_port,dst_port,protocol,tot_fwd_pkts,tot_bwd_pkts,flow_duration,fin_flag_cnt
***********,***********,12345,80,6,2,1,0.1,0
```

### UDP Flow (Unchanged)
```csv
src_ip,dst_ip,src_port,dst_port,protocol,tot_fwd_pkts,tot_bwd_pkts,flow_duration,fin_flag_cnt
***********,***********,53,12345,17,1,0,0.0,0
```

## How to Reproduce

1. **Process PCAP files**:
   ```bash
   cicflowmeter -f examples/mixed_traffic.pcap -c examples/mixed_traffic_flows.csv
   cicflowmeter -f examples/icmp_only.pcap -c examples/icmp_only_flows.csv
   ```

2. **Generate new test files**:
   ```bash
   python create_example_pcap.py
   python process_examples.py
   ```

## Traffic Types Demonstrated

### ICMP Types Included
- **Type 8/0**: Echo Request/Reply (ping)
- **Type 3**: Destination Unreachable (various codes)
- **Type 11**: Time Exceeded (TTL exceeded)
- **Type 5**: Redirect

### Flow Characteristics
- **Bidirectional ICMP flows**: Echo request/reply pairs grouped together
- **Unidirectional ICMP flows**: Error messages and notifications
- **Mixed protocol flows**: Demonstrates coexistence with TCP/UDP

## Performance Metrics

- **Processing Speed**: Similar to original TCP/UDP processing
- **Memory Usage**: Minimal increase due to ICMP flow tracking
- **Feature Extraction**: All 82 original features computed correctly
- **Flow Timeout**: Optimized 60s timeout for ICMP vs 240s for TCP/UDP

This demonstrates that the enhanced CICFlowMeter successfully processes ICMP traffic while maintaining full compatibility with existing TCP/UDP functionality and preserving the original 82-feature output format.
