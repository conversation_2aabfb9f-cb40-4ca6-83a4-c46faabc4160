TCPDUMP ICMP Flood PCAP Analysis Summary
=============================================

Source PCAP: examples/icmp_flood.pcap
Packets processed: 10000 (sample from 413,016 total)
Total flows extracted: 1
Features per flow: 82

Flow Distribution:
  ICMP flows: 1

Sample ICMP Flow:
  Source IP: ********
  Destination IP: ********
  Protocol: 2048
  Source Port: -1 (should be -1)
  Destination Port: -1 (should be -1)
  Forward Packets: 5001
  Backward Packets: 5000
  Flow Duration: 29.131503
  TCP Flags (should be -1): -1

TCPDUMP PCAP Compatibility Validation:
* TCPDUMP-generated PCAP successfully processed
* CookedLinux layer properly handled
* ICMP echo request/reply pairs detected
* Original 82 features maintained
* Large-scale processing capability confirmed
* Non-applicable fields properly set to -1
